import { defineStore } from 'pinia';

export const dataPipeline = defineStore('dataPipeline', {
	state: () => ({
        uploadFiles: false,
        projectInfo: {
            project_type: '',
            create_datetime: '',
            creator: 0,
            creator_name: '',
            customer_name: '',
            dept_belong_id: '',
            description: '',
            id: '',
            modifier: '',
            modifier_name: '',
            project_name: '', // "wuxi_asu_datapipeline"
            project_number: '', // "721002"
            template_type: 0,
            update_datetime: '',
            selectedProjectName: '',
            isSelectedChange: false,
        },
        fileUploadInfo: {
            isDoneFileUpload: false,
            isUploadingFile: false,
            percentProgressFileUpload: 0,
            analyzeStatsFileUpload: [
                { label: 'Files Uploaded', value: '0' },
                { label: 'Total Files', value: '0' },
                { label: 'Progress', value: '0%' },
                { label: 'Status', value: 'Not Started' }
            ],
            uploadingFiles: [] as Array<{
                name: string;
                size: number;
                isFolder: boolean;
                folderName: string;
            }>, // 存储正在上传的文件信息
        },
		minioInfo: {
            isDoneMinio: false,
            isSavingMinio: false,
            percentProgressMinio:0,
            analyzeStatsMinio: [
                { label: 'Files Processed', value: '0' },
                { label: 'Total Files', value: '0' },
                { label: 'Progress', value: '0%' },
                { label: 'Status', value: 'Processing' }
            ],
        },
        influxdbInfo: {
            isDoneParseSaveInfluxdb: false,
            isParsingSaveInfluxdb: false,
            percentProgressInfluxdb: 0,
            analyzeStatsInfluxdb: [
                { label: 'Files Parsed', value: '0' },
                { label: 'Total Files', value: '0' },
                { label: 'Progress', value: '0%' },
                { label: 'Status', value: 'Not Started' }
            ],
            binFile_startTime: '',
            binFile_endTime: '',
            binfile_Postgresql_id: '',
            binfile_Minio_version_id: '',
            influx_ubique_tagId: '',
        },
        filesPostgresqlInfo: [
            {
                "id": 0,
                "name": "",
                "size": 0,
                "path": "",
                "version": 0
            },
        ],
        binFiles_Minio_info: [] as Array<{
            object_name: string;
            version_id: string;
        }>,
        uploadFormData: null, // 存储上传文件的FormData
	}),
	actions: {},
});