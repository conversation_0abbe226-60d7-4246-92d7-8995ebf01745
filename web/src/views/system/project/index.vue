<template>
<div class="mx-auto px-4 py-6 flex min-h-[1024px]">
  
  <!-- 左侧项目列表 -->
  <div class="w-[25%] max-w-xs ml-[1%]"> <!-- 控制左侧固定宽度 -->
    <ProjectList ref="projectList" />
  </div>

  <!-- 右侧内容区，自动占满剩余空间 -->
  <div class="flex-1 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg p-6 border border-gray-100 mr-[1%]">
    <FileProcessor 
      :project-name="projectList?.selectedProject?.project_name"
      :project-type="projectList?.selectedProject?.project_type"
      :steps="projectList?.selectedProject?.steps"
      @analysis-complete="showResult = true"
    />
  </div>
</div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { Bell } from '@element-plus/icons-vue'
  import ProjectList from './components/ProjectList.vue'
  import FileProcessor from './components/FileProcessor.vue'
//   import AnalysisResults from './AnalysisResults.vue'
  
  const navItems = [
    // { name: 'Projects', path: '/projects', icon: 'Folder' },
    // { name: 'Dashboard', path: '/dashboard', icon: 'Monitor' },
    // { name: 'Analysis', path: '/analysis', icon: 'DataAnalysis' },
  ]
  const currentPath = '/projects'
  const showResult = ref(false)
  const projectList = ref()

  onMounted(() => {
    console.log('父组件 index.vue')
  });
  </script>
  
  <style scoped>
  router-link.router-link-active {
    @apply text-blue-600 font-medium;
  }
  </style>