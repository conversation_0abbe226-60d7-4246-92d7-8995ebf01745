<template>
  <div>
    <h2 class="text-lg font-medium">{{ projectType }} --> {{ projectName }}</h2>
    <!-- Pipeline步骤 -->
    <div class="flex items-center justify-center space-x-4">
      <div v-for="(step, index) in steps" :key="index" class="flex items-center">
        <div
          @click="handleStepClick(step)"
          :class="[
            'bg-gradient-to-br p-3 text-center w-40 shadow border transition-all duration-300 rounded-lg cursor-pointer',
            step.status === 'current' ? 'from-blue-100 to-indigo-100 border-blue-300 shadow-lg' :
            step.status === 'completed' ? 'from-emerald-50 to-teal-50 border-emerald-200 hover:shadow-md' :
            'from-gray-50 to-gray-100 border-gray-200 opacity-60 cursor-not-allowed'
          ]"
        >
          <el-icon :class="[
            'text-xl',
            step.status === 'current' ? 'text-blue-600' :
            step.status === 'completed' ? 'text-emerald-600' :
            'text-gray-400'
          ]">
            <component :is="step.icon" />
          </el-icon>
          <h4 class="font-medium text-sm mt-0.5">{{ step.name }}</h4>
          <p class="text-xs text-gray-500 mt-0.5">{{ step.description }}</p>
          
          <div v-if="step.status === 'completed'">
            <el-icon class="text-emerald-500"><Check /></el-icon>
          </div>
        </div>
        <el-icon v-if="index < steps.length - 1" :class="[
          'text-lg mx-2',
          step.status === 'completed' ? 'text-emerald-400' : 'text-gray-300'
        ]">
          <ArrowRight />
        </el-icon>
      </div>
    </div>

    <!-- 动态组件区域 -->
    <component 
      :is="currentComponent"
      :disabled="currentStepIndex !== 0"
      :currentStepIndex="currentStepIndex"
      :uploadFormData="uploadFormData"
      @uploadSuccess="handleUploadSuccess"
      @uploadError="handleUploadError"
      @processComplete="handleProcessingComplete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ArrowRight, Check } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 导入组件
import FileUpload from './FileUpload.vue'
import DataProcess from './DataProcess.vue'
import DataVisualization from './DataVisualization.vue'

const props = defineProps<{
  projectName: string,
  projectType: string,
}>()

const steps = ref([
  {
    name: 'File Upload',
    description: 'Upload data files to server',
    icon: 'Upload',
    status: 'current',
    index: 0
  },
  {
    name: 'Data Processing',
    description: 'Parse and write to InfluxDB',
    icon: 'DataAnalysis',
    status: 'pending',
    index: 1
  },
  {
    name: 'Visualization',
    description: 'Generate data analysis report',
    icon: 'PieChart',
    status: 'pending',
    index: 2
  },
])

const currentStepIndex = ref(0)

// 根据当前步骤返回对应组件
const currentComponent = computed(() => {
  switch (currentStepIndex.value) {
    case 0:
      return FileUpload
    case 1:
      return DataProcess
    case 2:
      return DataVisualization
    default:
      return FileUpload
  }
})

// 监听 projectName 和 projectType 的变化
watch([() => props.projectName, () => props.projectType], () => {
  // 重置所有步骤状态
  steps.value = steps.value.map((step) => ({
    ...step,
    status: step.index === 0 ? 'current' : 'pending'
  }))
  // 重置当前步骤索引
  currentStepIndex.value = 0
})

const handleStepClick = (step: any) => {
  // 获取最后一个已完成步骤的索引
  const lastCompletedIndex = Math.max(
    ...steps.value
      .map((s, index) => s.status === 'completed' ? index : -1)
      .filter(index => index !== -1)
  )

  // 允许点击已完成的步骤或当前步骤，且不能超过最后一个已完成的步骤
  if (step.index <= lastCompletedIndex || step.status === 'current') {
    currentStepIndex.value = step.index
    // 更新步骤状态
    steps.value = steps.value.map((s) => {
      if (s.index < step.index) {
        return { ...s, status: 'completed' }
      } else if (s.index === step.index) {
        return { ...s, status: 'current' }
      } else {
        return { ...s, status: step.index <= lastCompletedIndex ? 'completed' : 'pending' }
      }
    })
  }
}

const updateStepStatus = (completedStepIndex: number) => {
  steps.value = steps.value.map((step) => {
    if (step.index < completedStepIndex) {
      return { ...step, status: 'completed' }
    } else if (step.index === completedStepIndex) {
      return { ...step, status: 'current' }
    } else {
      return { ...step, status: 'pending' }
    }
  })
  currentStepIndex.value = completedStepIndex
}

// 处理各个步骤的完成事件
let uploadFormData
const handleUploadSuccess = (uploadData: any) => {
  ElMessage.success('文件上传成功')
  // 将formData传递给DataProcess组件
  uploadFormData = uploadData.formData;
  updateStepStatus(1)
}

const handleUploadError = (error: any) => {
  ElMessage.error('文件上传失败，请重试')
  console.error('Upload error:', error)
}

const handleProcessingComplete = () => {
  updateStepStatus(2)
}
</script>

<style scoped>
.el-table {
  --el-table-border-color: #e5e7eb;
  --el-table-header-bg-color: #f8faff;
  --el-table-row-hover-bg-color: #f0f7ff;
  border-radius: 0.75rem;
  overflow: hidden;
}
  
.el-button--danger {
  --el-button-hover-bg-color: #ef4444;
  --el-button-hover-border-color: #ef4444;
}
  
.el-table::before {
  display: none;
}
  
.el-table th.el-table__cell {
  background: linear-gradient(to right, #f0f7ff, #eef6ff);
}
  
.el-table .el-table__cell {
  transition: all 0.3s ease;
}

/* 添加拖拽相关动画 */
.border-2 {
  transition: all 0.3s ease;
}

.border-blue-400 {
  border-width: 3px;
}
</style>