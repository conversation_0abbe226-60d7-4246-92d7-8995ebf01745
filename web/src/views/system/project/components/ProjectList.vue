<template>
  <div class="bg-white/90 backdrop-blur-sm rounded-xl shadow-lg p-4 border border-gray-100" style="height: 100%;">
    <h2 class="text-lg font-medium mb-4">Projects</h2>
    
    <TabSelector
      v-model="selectedTemplate"
      :tabs="tags"
    />

    <!-- Search and Sort -->
    <div class="flex justify-between items-center mb-4">
      <SearchBar
        v-model="searchQuery"
        placeholder="Name/Number/Customer"
      />
      <SortDropdown
        :options="sortOptions"
        :current-key="sortKey"
        :current-order="sortOrder"
        @sort="handleSort"
      />
    </div>
    
    <!-- Add Project Button -->
    <div class="w-full mt-1">
      <el-button type="primary" style="width: 100%;" @click="openDialog">Add Project</el-button>
    </div>

    <div v-if="filteredProjects.length === 0" class="text-center py-8 text-gray-500">
      No projects found
    </div>
    
    <!-- Project List -->
    <div v-else class="h-[1050px] overflow-y-auto pr-2 space-y-2 border border-gray-300 rounded-lg border-solid border-2 mt-2.5 shadow-sm bg-white">
      <div
        v-for="project in sortedFilteredProjects"
        :key="project.id"
        class="p-4 rounded border cursor-pointer hover:bg-blue-50 hover:border-blue-200 transition-all duration-200 relative"
        :class="{
          'bg-blue-100 border-blue-300 shadow-md': selectedProject?.id === project.id,
          'bg-white border-gray-200': selectedProject?.id !== project.id,
        }"
      >
        <div class="absolute top-2 right-1 flex gap-0">
          <el-button
            type="primary"
            :icon="Edit"
            circle
            size="small"
            @click.stop="openEditDialog(project)"
          />
          <el-button
            type="danger"
            :icon="Delete"
            circle
            size="small"
            @click.stop="handleDelete(project)"
            style="margin-left: 4px"
          />
        </div>

        <div class="flex flex-col" @click="selectProject(project)">
          <div class="flex items-center mb-3">
            <h3 class="font-medium">{{ project.project_name }}</h3>
            <el-tag 
              size="small" 
              class="ml-2"
              :style="{ backgroundColor: getTagColor(project.template_type), color: '#fff', border: 'none' }"
            >
              {{ getTagLabel(project.template_type) }}
            </el-tag>
          </div>
          
          <div class="space-y-2 text-sm text-gray-600">
            <div class="flex items-center">
              <el-icon class="mr-2"><Document /></el-icon>
              Project Number: {{ project.project_number }}
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2"><User /></el-icon>
              Customer: {{ project.customer_name }}
            </div>
            <div class="flex items-center">
              <el-icon class="mr-2"><Timer /></el-icon>
              Last Updated: {{ formatDate(project.update_datetime) }}
            </div>
          </div>
        </div>
      </div>
    </div>
    
  </div>

  <!-- Add Project Dialog -->
  <el-dialog
    v-model="showAddProjectDialog"
    title="Add New Project"
    width="500px"
  >
    <el-form :model="newProject" label-position="top" :rules="rules" ref="formRef">
      <el-form-item label="Project Name" prop="project_name">
        <el-input v-model="newProject.project_name" placeholder="Enter project name" />
      </el-form-item>
      <el-form-item label="Customer Name" prop="customer_name">
        <el-input v-model="newProject.customer_name" placeholder="Enter customer name" />
      </el-form-item>
      <el-form-item label="Project Number" prop="project_number">
        <el-input v-model="newProject.project_number" placeholder="Enter project number" />
      </el-form-item>
      <el-form-item label="Template Type" prop="template_type">
        <el-select v-model="newProject.template_type" placeholder="Select template type" class="w-full">
          <el-option
            v-for="tag in tags.filter(tag => tag.value !== -1)"
            :key="tag.value"
            :label="tag.label"
            :value="tag.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showAddProjectDialog = false">Cancel</el-button>
        <el-button type="primary" @click="addProject" :loading="loading.addProject">Create</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- Edit Project Dialog -->
  <el-dialog
    v-model="showEditProjectDialog"
    title="Edit Project"
    width="500px"
  >
    <el-form :model="editingProject" label-position="top" :rules="rules" ref="editFormRef">
      <el-form-item label="Project Name" prop="project_name">
        <el-input v-model="editingProject.project_name" placeholder="Enter project name" />
      </el-form-item>
      <el-form-item label="Customer Name" prop="customer_name">
        <el-input v-model="editingProject.customer_name" placeholder="Enter customer name" />
      </el-form-item>
      <el-form-item label="Project Number" prop="project_number">
        <el-input v-model="editingProject.project_number" placeholder="Enter project number" />
      </el-form-item>
      <el-form-item label="Template Type" prop="template_type">
        <el-select v-model="editingProject.template_type" placeholder="Select template type" class="w-full">
          <el-option
            v-for="tag in tags.filter(tag => tag.value !== -1)"
            :key="tag.value"
            :label="tag.label"
            :value="tag.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showEditProjectDialog = false">Cancel</el-button>
        <el-button type="primary" @click="updateProject" :loading="loading.updateProject">Update</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, toRaw, watch } from 'vue'
import { Document, User, Timer, Edit, Delete } from '@element-plus/icons-vue'
import TabSelector from './TabSelector.vue'
import SearchBar from './SearchBar.vue'
import SortDropdown from './SortDropdown.vue'
import { ElMessage, FormInstance, FormRules, ElMessageBox } from 'element-plus'
import * as projectApi from '../api'
import { dataPipeline } from '/@/stores/dataPipeline';

interface Project {
  id: number
  project_type: string
  project_name: string
  project_number: string
  customer_name: string
  template_type: number
  update_datetime?: string
  create_datetime?: string
}

interface SortOption {
  key: string
  label: string
  order: "asc" | "desc"
}

const tags = [
  { 
    label: 'All',
    value: -1,
    color: '#28A745'
  },
  { 
    label: 'EP2002', 
    value: 0,
    color: '#007BFF'
  },
  { 
    label: 'ASU', 
    value: 1,
    color: '#FFC107'
  },
  { 
    label: 'PHM', 
    value: 2,
    color: '#DC3545'
  }
]

const projects = ref<Project[]>([])
const selectedProject = ref<Project | null>(null)
const selectedTemplate = ref<number>(-1)
const searchQuery = ref('')
const formRef = ref<FormInstance>()

const showAddProjectDialog = ref(false)
const newProject = ref({
  project_name: '',
  customer_name: '',
  project_number: '',
  template_type: 0
})

const loading = ref({
  addProject: false,
  fetchProjects: false,
  updateProject: false
})

const sortKey = ref<keyof Project>('update_datetime')
const sortOrder = ref<'asc' | 'desc'>('desc')

const sortOptions: SortOption[] = [
  { key: 'project_number', label: 'Project Number ↑', order: 'asc' },
  { key: 'project_number', label: 'Project Number ↓', order: 'desc' },
  { key: 'project_name', label: 'Project Name ↑', order: 'asc' },
  { key: 'project_name', label: 'Project Name ↓', order: 'desc' },
  { key: 'customer_name', label: 'Customer Name ↑', order: 'asc' },
  { key: 'customer_name', label: 'Customer Name ↓', order: 'desc' },
  { key: 'update_datetime', label: 'Last Updated ↑', order: 'asc' },
  { key: 'update_datetime', label: 'Last Updated ↓', order: 'desc' }
]

const rules = {
  project_name: [
    { required: true, message: 'Please enter project name', trigger: 'blur' },
  ],
  customer_name: [
    { required: true, message: 'Please enter customer name', trigger: 'blur' },
  ],
  project_number: [
    { required: true, message: 'Please enter project number', trigger: 'blur' },
  ],
  template_type: [
    { required: true, message: 'Please select template type', trigger: 'change' },
  ]
}

const handleSort = ({ key, order }: { key: string, order: "asc" | "desc" }) => {
  sortKey.value = key as keyof Project
  sortOrder.value = order
}

const selectProject = (project: Project) => {
  selectedProject.value = project
  const projectTypeTag = tags.find(tag => tag.value === project.template_type)
  selectedProject.value.project_type = projectTypeTag.label
}

const formatDate = (dateString?: string) => {
  if (!dateString) return 'Unknown';
  // Format the date as needed
  const date = new Date(dateString);
  return date.toLocaleString();
}

const getTagLabel = (templateType: number) => {
  const tag = tags.find(tag => tag.value === templateType)
  return tag ? tag.label : 'Unknown'
}

const getTagColor = (templateType: number) => {
  const tag = tags.find(tag => tag.value === templateType)
  return tag ? tag.color : '#909399' // Default gray color if tag not found
}

// 把项目相关信息存到pinia里面
const store = dataPipeline();
const writeProInfoToPinia = (selectedSingleProInfo) => {
  console.log('selectedSingleProInfo ============= ', selectedSingleProInfo)
  const projectTypeTag = tags.find(tag => tag.value === selectedSingleProInfo.template_type)
  selectedProject.value.project_type = projectTypeTag.label // 立即赋值, 使前端显示如: 'EP2002 : EP2002 v1.1 - Pipeline流程'

  // 存到pinia里
  store.$state.projectInfo.project_type = projectTypeTag.label
  store.$state.projectInfo.create_datetime = selectedSingleProInfo.create_datetime
  store.$state.projectInfo.creator = selectedSingleProInfo.creator
  store.$state.projectInfo.creator_name = selectedSingleProInfo.creator_name
  store.$state.projectInfo.customer_name = selectedSingleProInfo.customer_name
  store.$state.projectInfo.dept_belong_id = selectedSingleProInfo.dept_belong_id
  store.$state.projectInfo.description = selectedSingleProInfo.description
  store.$state.projectInfo.id = selectedSingleProInfo.id
  store.$state.projectInfo.modifier = selectedSingleProInfo.modifier
  store.$state.projectInfo.modifier_name = selectedSingleProInfo.modifier_name
  store.$state.projectInfo.project_name = selectedSingleProInfo.project_name
  store.$state.projectInfo.project_number = selectedSingleProInfo.project_number
  store.$state.projectInfo.template_type = selectedSingleProInfo.template_type
  store.$state.projectInfo.update_datetime = selectedSingleProInfo.update_datetime
  store.$state.projectInfo.selectedProjectName = selectedProject.value.project_name // 点击不同项目, 更改项目名字 selectedProject
}

const fetchSinglePro = (projects, selectedProjectName, selectedProjectTypeNum) => {
  // 根据selectedPro， 从项目List里查找
  console.log('筛选依据 selectedProjectName = ', selectedProjectName, 'selectedProjectTypeNum = ', selectedProjectTypeNum)
  const result = projects.find(project => 
    project.project_name === selectedProjectName && 
    project.template_type === selectedProjectTypeNum
  );
  return result
}

// 根据所选项目的name和type，从项目列表里筛选Obj, 更新pinia里的项目信息
watch(selectedProject, (val) => {
  console.log('触发watch(), selectedProject = ', toRaw(val))
  const selectedProjectTypeNum = selectedProject.value.template_type
  const selectedProjectName = selectedProject.value.project_name
  const singleProInfo = fetchSinglePro(toRaw(projects.value),selectedProjectName, selectedProjectTypeNum)
  writeProInfoToPinia(singleProInfo)
});


// 获取所有项目列表
const fetchProjects = async () => {
  console.log('获取所有项目list fetchProjects() ')
  loading.value.fetchProjects = true
  try {
    const response = await projectApi.getProjects()
    if (response.code === 2000) {
      projects.value = response.data

      console.log('projects.value === ', projects.value)


      // if (projects.value.length > 0 && !selectedProject.value) {
      if (projects.value.length > 0 ) {
        // 初始化, 默认第一个item为selectedProject

        console.log('fetchProjects() -- 重新赋值默认第一个selectedPorject  selectedProject')
        selectedProject.value = projects.value[0]
      }
    } else {
      ElMessage.error(response.msg || 'Failed to fetch projects')
    }
  } catch (error) {
    console.error('Error fetching projects:', error)
    ElMessage.error('Failed to fetch projects')
  } finally {
    loading.value.fetchProjects = false
  }
}

const addProject = async () => {
  console.log('执行 addProject() ')

  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value.addProject = true
      try {
        console.log("创建新项目", newProject.value)
        const response = await projectApi.addProject(newProject.value)
        if (response.code === 2000) {
          ElMessage.success('Project added successfully')
          showAddProjectDialog.value = false
          // Reset form
          newProject.value = {
            project_name: '',
            customer_name: '',
            project_number: '',
            template_type: 0
          }
          // Refresh projects list

          console.log('执行 addProject()里的 fetchProjects() ')
          await fetchProjects()
        } else {
          ElMessage.error(response.msg || 'Failed to add project')
        }
      } catch (error) {
        console.error('Error adding project:', error)
        ElMessage.error('Failed to add project')
      } finally {
        loading.value.addProject = false
      }
    }
  })
}

const openDialog = () => {
  showAddProjectDialog.value = true
}

const filteredProjects = computed(() => {
  let result = projects.value

  if (selectedTemplate.value !== -1) {
    result = result.filter(project => project.template_type === selectedTemplate.value)
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(project => 
      project.project_name.toLowerCase().includes(query) || 
      project.project_number.toLowerCase().includes(query) ||
      project.customer_name.toLowerCase().includes(query)
    )
  }

  return result
})

const sortedFilteredProjects = computed(() => {
  return [...filteredProjects.value].sort((a, b) => {
    const modifier = sortOrder.value === 'asc' ? 1 : -1
    const aValue = a[sortKey.value];
    const bValue = b[sortKey.value];
    
    // Handle potentially undefined values
    if (aValue === undefined && bValue === undefined) return 0;
    if (aValue === undefined) return 1 * modifier;
    if (bValue === undefined) return -1 * modifier;
    
    if (aValue < bValue) return -1 * modifier
    if (aValue > bValue) return 1 * modifier
    return 0
  })
})

// Fetch projects when component is mounted
onMounted(() => {
  fetchProjects()
})

const showEditProjectDialog = ref(false)
const editFormRef = ref<FormInstance>()
const editingProject = ref<Project>({
  id: 0,
  project_name: '',
  customer_name: '',
  project_number: '',
  template_type: 0,
  project_type: ''
})

const handleDelete = async (project: Project) => {
  try {
    await ElMessageBox.confirm(
      `Are you sure to delete project "${project.project_name}"?`,
      'Warning',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
    )
    
    const response = await projectApi.delSingleProject(project.id)
    if (response.code === 2000) {
      ElMessage.success('Project deleted successfully')
      if (selectedProject.value?.id === project.id) {
        selectedProject.value = null
      }
      await fetchProjects()
    } else {
      ElMessage.error(response.msg || 'Failed to delete project')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting project:', error)
      ElMessage.error('Failed to delete project')
    }
  }
}

const openEditDialog = (project: Project) => {
  editingProject.value = { ...project }
  showEditProjectDialog.value = true
}

const updateProject = async () => {
  if (!editFormRef.value) return
  
  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value.updateProject = true
      try {
        const response = await projectApi.updateSingleProject(editingProject.value)
        if (response.code === 2000) {
          ElMessage.success('Project updated successfully')
          showEditProjectDialog.value = false
          // 重置表单
          editingProject.value = {
            id: 0,
            project_name: '',
            customer_name: '',
            project_number: '',
            template_type: 0,
            project_type: ''
          }
          // 刷新项目列表
          await fetchProjects()
        } else {
          ElMessage.error(response.msg || 'Failed to update project')
        }
      } catch (error) {
        console.error('Error updating project:', error)
        ElMessage.error('Failed to update project')
      } finally {
        loading.value.updateProject = false
      }
    }
  })
}

defineExpose({
  selectedProject,
  fetchProjects
})
</script>

<style scoped>

</style>

