FileUpload.vue:411 开始处理拖拽文件，项目数量: 1
FileUpload.vue:426 检测到文件夹: test
FileUpload.vue:360 开始读取文件夹: test
FileUpload.vue:374 文件夹 test 当前批次包含 6 个条目
FileUpload.vue:348 读取文件: LOG00741_副本.BIN
FileUpload.vue:351 成功读取文件: LOG00741_副本.BIN 大小: 413760
FileUpload.vue:348 读取文件: LOG00760.BIN
FileUpload.vue:351 成功读取文件: LOG00760.BIN 大小: 534592
FileUpload.vue:348 读取文件: LOG00741.BIN
FileUpload.vue:351 成功读取文件: LOG00741.BIN 大小: 413760
FileUpload.vue:348 读取文件: LOG00760_副本.BIN
FileUpload.vue:351 成功读取文件: LOG00760_副本.BIN 大小: 534592
FileUpload.vue:348 读取文件: LOG00760_副本2.BIN
FileUpload.vue:351 成功读取文件: LOG00760_副本2.BIN 大小: 534592
FileUpload.vue:348 读取文件: LOG00760_副本3.BIN
FileUpload.vue:351 成功读取文件: LOG00760_副本3.BIN 大小: 534592
FileUpload.vue:369 文件夹 test 读取完成，共 6 个文件
FileUpload.vue:448 最终获取到的文件总数: 6
FileUpload.vue:451 准备上传文件: {fileCount: 6, isFolder: true, folderName: 'test', fileNames: Array(6)}
DataProcess.vue:620 执行 initProgressInfo()
DataProcess.vue:673 !isAllDone ==============  true
DataProcess.vue:676 onMounted() 检查是否需要开始解析
DataProcess.vue:677 isDoneFileUpload.value 检查文件是否已上传到后端 ==============  false
DataProcess.vue:556 等待文件上传完成...
DataProcess.vue:199 SSE建立连接 - 文件上传进度监控
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 0, uploaded_files: 0, total_files: 6, message: '已上传 0/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 0, uploaded_files: 0, total_files: 6, message: '已上传 0/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 0, uploaded_files: 0, total_files: 6, message: '已上传 0/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 0, uploaded_files: 0, total_files: 6, message: '已上传 0/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 16, uploaded_files: 1, total_files: 6, message: '已上传 1/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 16, uploaded_files: 1, total_files: 6, message: '已上传 1/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 33, uploaded_files: 2, total_files: 6, message: '已上传 2/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 33, uploaded_files: 2, total_files: 6, message: '已上传 2/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 50, uploaded_files: 3, total_files: 6, message: '已上传 3/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 50, uploaded_files: 3, total_files: 6, message: '已上传 3/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 66, uploaded_files: 4, total_files: 6, message: '已上传 4/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 66, uploaded_files: 4, total_files: 6, message: '已上传 4/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 83, uploaded_files: 5, total_files: 6, message: '已上传 5/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 83, uploaded_files: 5, total_files: 6, message: '已上传 5/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'uploading', progress: 100, uploaded_files: 6, total_files: 6, message: '已上传 6/6 个文件'}
DataProcess.vue:204 文件上传进度: {status: 'completed', progress: 100, uploaded_files: 6, total_files: 6, message: '文件上传完成', …}
DataProcess.vue:235 文件上传完成
DataProcess.vue:536 文件上传完成，开始 Minio 和 InfluxDB 处理
DataProcess.vue:598 saveDataIoMinio() 开始
DataProcess.vue:294 SSE建立连接 - minio eventSource2
DataProcess.vue:299 data=========== {progress: 16, current: 1, total: 6, binFiles_Minio_info: Array(1), status: 'Processing'}
DataProcess.vue:299 data=========== {progress: 33, current: 2, total: 6, binFiles_Minio_info: Array(2), status: 'Processing'}
DataProcess.vue:299 data=========== {progress: 50, current: 3, total: 6, binFiles_Minio_info: Array(3), status: 'Processing'}
DataProcess.vue:299 data=========== {progress: 66, current: 4, total: 6, binFiles_Minio_info: Array(4), status: 'Processing'}
DataProcess.vue:299 data=========== {progress: 83, current: 5, total: 6, binFiles_Minio_info: Array(5), status: 'Processing'}
DataProcess.vue:299 data=========== {progress: 100, current: 6, total: 6, binFiles_Minio_info: Array(6), status: 'Completed'}
DataProcess.vue:302 Minio 上传完成
DataProcess.vue:303 data=========== {progress: 100, current: 6, total: 6, binFiles_Minio_info: Array(6), status: 'Completed'}
DataProcess.vue:317 store.$state.binFiles_Minio_info ====  Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}}
DataProcess.vue:611 saveDataIoMinio() 真正完成
DataProcess.vue:563 Minio 上传完成
DataProcess.vue:566 Influxdb 保存完成
