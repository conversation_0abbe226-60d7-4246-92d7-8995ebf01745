<template>
  <div class="mt-8">
    <!-- 文件上传区域 -->
        <div
          @drop.prevent="handleDrop"
          @dragover.prevent="handleDragover"
          @dragleave.prevent="handleDragleave"
          :class="[
            'border-2 border-dashed rounded-xl p-4 text-center bg-gradient-to-br transition-colors duration-300 mb-6',
            isDragging ? 'border-blue-400 from-blue-100 to-indigo-100' : 'border-blue-200 from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100'
          ]"
        >
          <input
            type="file"
            ref="fileInput"
            multiple
            class="hidden"
            @change="handleFileSelect"
            accept=".csv,.xlsx,.pdf,.bin"
          />
          <input
            type="file"
            ref="folderInput"
            multiple
            class="hidden"
            @change="handleFolderSelect"
            webkitdirectory
            accept=".csv,.xlsx,.pdf,.bin"
          />
          <el-icon class="text-gray-400 text-4xl mb-3"> 
            <Upload />
          </el-icon>
          <p class="text-gray-600">Drag file to upload</p>
          <div class="flex justify-center space-x-4">
            <el-dropdown @command="handleUploadCommand">
              <button
                :disabled="currentStepIndex !== 0"
                :class="[
                  'mt-3 px-4 py-2 bg-gradient-to-r text-white rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 !rounded-button whitespace-nowrap flex items-center',
                  currentStepIndex === 0 
                    ? 'from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700'
                    : 'from-gray-400 to-gray-500 cursor-not-allowed'
                ]"
              >
                <el-icon class="mr-1"><Upload /></el-icon>
                Files Upload
                <el-icon class="ml-1"><ArrowDown /></el-icon>
              </button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="file">
                    <el-icon><Document /></el-icon>
                    <span>Files Upload</span>
                  </el-dropdown-item>
                  <el-dropdown-item command="folder">
                    <el-icon><FolderAdd /></el-icon>
                    <span>Filefolder Upload</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <button
              @click="showParserConfig"
              class="mt-3 px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-lg hover:from-emerald-600 hover:to-teal-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 !rounded-button whitespace-nowrap"
            >
              <el-icon class="mr-1"><Setting /></el-icon>
              Parse Configuration
            </button>
          </div>
        </div>
        <div class="mb-6">
          <h3 class="font-medium mb-4">Upload History</h3>
            <div class="border border-gray-300 rounded-lg shadow-sm bg-white">
              <el-table
                :data="fileList"
                style="width: 100%;"
                border
                height="800px"
                @selection-change="handleSelectionChange"
                @row-click="handleRowClick"
                :default-sort="{ prop: 'last_modified', order: 'descending' }"
              >
              <!-- <el-table-column type="selection" width="55" /> -->
              <el-table-column 
                prop="name" 
                label="File Name" 
                sortable
                min-width="200"
                width="100"
              >
                <template #default="scope">
                  <div class="flex items-center">
                    <el-icon class="mr-2" :size="20">
                      <component :is="getFileIcon(scope.row)" />
                    </el-icon>
                    <span :class="{'text-blue-600': scope.row.isFolder}">
                      {{ scope.row.name }}
                      <!-- <el-tag size="small" type="success" v-if="scope.row.is_latest">Latest</el-tag> -->
                    </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="size" 
                label="Size" 
                sortable
                align="center" 
                width="100">
                <template #default="scope">
                  {{ formatFileSize(scope.row.size) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="last_modified"
                label="Upload Time"
                sortable
                align="center" 
                width="100"
                :sort-orders="['descending', 'ascending']"
              >
                <template #default="scope">
                  {{ formatDate2(scope.row.last_modified) ? formatDate2(scope.row.last_modified) : '' }}
                </template>
              </el-table-column>
              <el-table-column
                prop="start_time"
                label="binFile_StartTime"
                sortable
                align="center" 
                width="100"
                :sort-orders="['descending', 'ascending']"
              >
                <template #default="scope">
                  <!-- {{ formatDate(scope.row.start_time) }} -->
                  {{ scope.row.start_time ? formatDate1(scope.row.start_time) : '' }}
                </template>
              </el-table-column>
              <el-table-column
                prop="end_time"
                label="binFile_EndTime"
                sortable
                align="center" 
                width="100"
                :sort-orders="['descending', 'ascending']"
              >
                <template #default="scope">
                  <!-- {{ formatDate(scope.row.end_time) }} -->
                  {{ scope.row.start_time ? formatDate1(scope.row.end_time) : '' }}
                </template>
              </el-table-column>
              <!-- <el-table-column
                prop="file_Minio_version_id"
                label="file_Minio_version_id"
                sortable
                width="100"
                :sort-orders="['descending', 'ascending']"
              >
                <template #default="scope">
                  {{ scope.row.file_Minio_version_id ? scope.row.file_Minio_version_id : '' }}
                </template>
              </el-table-column>
              <el-table-column
                prop="file_Postgresql_id"
                label="file_Postgresql_id"
                sortable
                width="100"
                :sort-orders="['descending', 'ascending']"
              >
                <template #default="scope">
                  {{ scope.row.file_Postgresql_id ? scope.row.file_Postgresql_id : '' }}
                </template>
              </el-table-column>
              <el-table-column
                prop="influx_ubique_tagId"
                label="influx_ubique_tagId"
                sortable
                width="100"
                :sort-orders="['descending', 'ascending']"
              >
                <template #default="scope">
                  {{ scope.row.influx_ubique_tagId ? scope.row.influx_ubique_tagId : '' }}
                </template>
              </el-table-column> -->
            </el-table>
          </div>
        </div>

        <!-- 数据可视化弹窗 -->
        <el-dialog
          v-model="dialogVisible"
          title="Data Visualization"
          width="90%"
          :before-close="handleDialogClose"
          class="data-visualization-dialog"
        >
          <DataVisualization
            :key="dialogKey"
            :file-data="selectedRowData"
            :is-from-click="true"
          />
        </el-dialog>
      </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted, watch, toRaw } from 'vue';
import { Upload, Setting, Loading, FolderAdd, Document, ArrowDown, Download, Folder, FolderOpened } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { dataPipeline } from '/@/stores/dataPipeline';
import DataVisualization from './DataVisualization.vue';

// 假设你使用 JWT 或其他认证方式，从存储中获取 token
import {Session} from '/@/utils/storage';
import { getBaseURL } from '/@/utils/baseUrl';

const store = dataPipeline();

const baseBackendUrl = getBaseURL()
const props = defineProps<{
	disabled?: boolean;
	currentStepIndex: number;
}>();

const emit = defineEmits(['uploadSuccess', 'uploadError']);

const fileInput = ref<HTMLInputElement | null>(null);
const folderInput = ref<HTMLInputElement | null>(null);
const isDragging = ref(false);
const fileList = ref<any[]>([]);
const selectedFiles = ref<any[]>([]);

// 弹窗相关状态
const dialogVisible = ref(false);
const selectedRowData = ref<any>(null);
const dialogKey = ref(0); // 用于强制重新渲染弹窗组件

// 文件类型和大小限制
const allowedTypes = ['text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/pdf'];
const maxFileSize = 10 * 1024 * 1024; // 10MB


const progress = ref(0); // 进度条值

// 确保在组件卸载时关闭SSE连接
onUnmounted(() => {
});

// 处理文件选择
const handleFileSelect = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files) {
    handleFiles(Array.from(input.files));
  }
};

// 处理文件拖拽离开
const handleDragleave = () => {
  isDragging.value = false;
};

// 删除文件
const deleteFile = (file: any) => {
  const index = fileList.value.indexOf(file);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }
};

// 修改文件处理方法, 注意: 此处保存只是保存到后端
let file_dataBase_info = []
const handleFiles = async (files: File[], isFolder: boolean = false, folderName: string = '') => {
  // 验证文件
  const validFiles = files.filter(file => {
    // 获取文件扩展名
    const fileExt = file.name.toLowerCase().split('.').pop() || '';
    const allowedExts = ['csv', 'xlsx', 'pdf', 'bin'];

    // 检查文件类型
    const fileType = file.type || '';
    if (!allowedTypes.includes(fileType) && !allowedExts.includes(fileExt)) {
      ElMessage.error(`不支持的文件类型: ${file.name}`);
      return false;
    }
    return true;
  });

  if (validFiles.length === 0) {
    ElMessage.warning('未选择任何有效文件');
    return;
  }

  // 构建 formData
  const formData = new FormData();
  validFiles.forEach(file => {
    formData.append('files', file);
  });

  // 添加文件夹相关信息
  formData.append('isFolder', isFolder.toString());
  if (isFolder && folderName) {
    formData.append('folderName', folderName);
  }

  // 立即更新 pinia 状态并跳转到 DataProcess 组件
  const store = dataPipeline();
  store.$state.uploadFiles = true;
  store.$state.fileUploadInfo.isUploadingFile = true;
  store.$state.fileUploadInfo.isDoneFileUpload = false;
  store.$state.fileUploadInfo.percentProgressFileUpload = 0;
  store.$state.fileUploadInfo.analyzeStatsFileUpload = [
    { label: 'Files Uploaded', value: '0' },
    { label: 'Total Files', value: validFiles.length.toString() },
    { label: 'Progress', value: '0%' },
    { label: 'Status', value: 'Uploading' }
  ];
  store.$state.fileUploadInfo.uploadingFiles = validFiles.map(file => ({
    name: file.name,
    size: file.size,
    isFolder,
    folderName
  }));

  // 立即触发组件切换，传递 formData
  emit('uploadSuccess', { fileList: fileList.value, formData });
};

const showParserConfig = () => {
	ElMessage.info('解析器配置功能开发中...');
};

// 处理上传命令
const handleUploadCommand = (command: string) => {
  if (props.currentStepIndex !== 0) return;
  
  switch (command) {
    case 'file':
      fileInput.value?.click();
      break;
    case 'folder':
      folderInput.value?.click();
      break;
  }
};

// 处理拖拽进入
const handleDragover = (e: DragEvent) => {
	if (props.currentStepIndex === 0) {
		isDragging.value = true;
	}
};

// 递归读取文件夹内容
const readFolderContents = async (entry: any): Promise<File[]> => {
  if (entry.isFile) {
    // 如果是文件，直接添加到列表
    console.log('读取文件:', entry.name);
    return new Promise((resolve, reject) => {
      entry.file((file: File) => {
        console.log('成功读取文件:', file.name, '大小:', file.size);
        resolve([file]);
      }, (error: any) => {
        console.error('读取文件失败:', entry.name, error);
        resolve([]); // 即使失败也返回空数组，不中断整个过程
      });
    });
  } else if (entry.isDirectory) {
    // 如果是文件夹，递归读取内容
    console.log('开始读取文件夹:', entry.name);
    const dirReader = entry.createReader();
    const allFiles: File[] = [];

    return new Promise((resolve, reject) => {
      const readAllEntries = () => {
        dirReader.readEntries(async (entries: any[]) => {
          if (entries.length === 0) {
            // 没有更多条目了，返回结果
            console.log(`文件夹 ${entry.name} 读取完成，共 ${allFiles.length} 个文件`);
            resolve(allFiles);
            return;
          }

          console.log(`文件夹 ${entry.name} 当前批次包含 ${entries.length} 个条目`);

          try {
            // 处理当前批次的条目
            for (const childEntry of entries) {
              const childFiles = await readFolderContents(childEntry);
              allFiles.push(...childFiles);
            }

            // 继续读取下一批条目（某些浏览器会分批返回）
            readAllEntries();
          } catch (error) {
            console.error('处理文件夹条目时出错:', entry.name, error);
            resolve(allFiles); // 即使出错也返回已获取的文件
          }
        }, (error: any) => {
          console.error('读取文件夹条目失败:', entry.name, error);
          resolve(allFiles); // 即使失败也返回已获取的文件
        });
      };

      readAllEntries();
    });
  }

  return [];
};

// 处理文件拖放
const handleDrop = async (e: DragEvent) => {
  isDragging.value = false;
  if (props.currentStepIndex !== 0) return;

  const items = e.dataTransfer?.items;
  const files = e.dataTransfer?.files;
  if (!items || !files) return;

  console.log('开始处理拖拽文件，项目数量:', items.length);

  try {
    let allFiles: File[] = [];
    let hasFolder = false;
    let folderName = '';

    // === 主要改动点：先尝试用 webkitGetAsEntry() 判断是否有文件夹 ===
    for (let i = 0; i < items.length; i++) {
      const entry = items[i].webkitGetAsEntry();
      if (!entry) continue;

      if (entry.isDirectory) {
        hasFolder = true;
        folderName = entry.name;
        console.log('检测到文件夹:', folderName);
        break; // 只要有一个文件夹就停止检查
      }
    }

    // === 如果有文件夹，则递归读取内容 ===
    if (hasFolder) {
      for (let i = 0; i < items.length; i++) {
        const entry = items[i].webkitGetAsEntry();
        if (!entry) continue;

        if (entry.isDirectory) {
          const folderFiles = await readFolderContents(entry);
          allFiles.push(...folderFiles);
        }
      }
    } else {
      // === 如果没有文件夹，则直接使用 files 列表获取所有文件 ===
      console.log('未检测到文件夹，使用传统方式读取文件');
      allFiles = Array.from(files);
    }

    console.log('最终获取到的文件总数:', allFiles.length);

    if (allFiles.length > 0) {
      console.log('准备上传文件:', {
        fileCount: allFiles.length,
        isFolder: hasFolder,
        folderName: folderName,
        fileNames: allFiles.map(f => f.name)
      });

      if (hasFolder) {
        handleFiles(allFiles, true, folderName);
      } else {
        handleFiles(allFiles, false);
      }
    } else {
      console.warn('未检测到任何有效文件');
      ElMessage.warning('未检测到任何有效文件');
    }
  } catch (error) {
    console.error('处理拖拽文件时出错:', error);
    ElMessage.error('处理拖拽文件时出错，请重试');
  }
};

// 修改文件夹选择处理
const handleFolderSelect = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files) {
    const files = Array.from(input.files);
    if (files.length === 0) {
      ElMessage.warning('未选择任何有效文件');
      return;
    }
    
    // 获取文件夹名称 (取第一个文件的路径的第一部分)
    const firstFile = files[0];
    const folderPath = firstFile.webkitRelativePath;
    const folderName = folderPath.split('/')[0];
    
    // 使用文件夹模式处理文件
    handleFiles(files, true, folderName);
  }
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

// 格式化日期
const formatDate1 = (date: string) => {
  return new Date(date).toLocaleString();
};

// 格式化日期
const formatDate2 = (dateStr: string) => {
  // 替换非标准格式的时间字符串为 ISO 格式（便于解析）
  let isoStr = dateStr;
  if (dateStr.includes('/')) {
    // 如果是 '2025/06/16 06:23:08' 这种格式，转成 '2025-06-16T06:23:08'
    isoStr = dateStr.replace(/\//g, '-').replace(' ', 'T');
  }

  const utcDate = new Date(isoStr);

  // 加上 8 小时（UTC+8）
  // const localDate = new Date(utcDate.getTime() + 8 * 60 * 60 * 1000);
  const localDate = new Date(utcDate.getTime() + 8 * 60 * 60 * 1000);

  // 返回本地格式化字符串（带 UTC+8）
  return localDate.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  }).replace(/(\d+)\/(\d+)\/(\d+)/, '$1/$2/$3'); // 保持斜杠格式
};

// 格式化日期 - 将UTC时间转换为北京时间(UTC+8)
const formatDate = (date: string) => {
  if (!date) return '';

  const d = new Date(date);
  // 添加8小时转换为北京时间
  const beijingTime = new Date(d.getTime());

  // 格式化为 YYYY/MM/DD HH:mm:ss
  const year = beijingTime.getUTCFullYear();
  const month = String(beijingTime.getUTCMonth() + 1).padStart(2, '0');
  const day = String(beijingTime.getUTCDate()).padStart(2, '0');
  const hours = String(beijingTime.getUTCHours()).padStart(2, '0');
  const minutes = String(beijingTime.getUTCMinutes()).padStart(2, '0');
  const seconds = String(beijingTime.getUTCSeconds()).padStart(2, '0');

  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
};

// 判断是否为文件夹
const isFolder = (file: any) => {
  return file.path.endsWith('/') || !file.path.includes('noFolderFileList/');
};

// 获取文件图标
const getFileIcon = (file: any) => {
  if (isFolder(file)) {
    return FolderOpened;
  }
  return Document;
};

// 处理表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedFiles.value = selection;
};

// 处理表格行点击事件
const handleRowClick = (row: any) => {
  console.log('点击表格行，传入数据:', row);
  // 更新数据和key，强制重新渲染组件
  selectedRowData.value = row;
  dialogKey.value = Date.now(); // 使用时间戳确保key的唯一性
  dialogVisible.value = true;
};

// 处理对话框关闭事件
const handleDialogClose = () => {
  console.log('关闭弹窗，清空数据');
  dialogVisible.value = false;
  selectedRowData.value = null;
};

// 修改处理文件列表数据的方法
const processFileList = (files: any[]) => {
  const expandedFiles: any[] = [];
  
  files.forEach(file => {
    if (file.versions && file.versions.length > 0) {
      // 对于有多个版本的文件，为每个版本创建一个条目
      file.versions.forEach((version: any) => {
        expandedFiles.push({
          ...file,
          name: `${file.name}`,
          last_modified: formatDate(version.last_modified),
          version_id: version.version_id,
          is_latest: version.is_latest,
          // 使用版本中的时间和ID信息，而不是文件级别的
          start_time: formatDate(version.start_time),
          end_time: formatDate(version.end_time),
          isFolder: false,
          file_Minio_version_id: version.file_Minio_version_id,
          file_Postgresql_id: version.file_Postgresql_id,
          influx_ubique_tagId: version.influx_ubique_tagId,
        });
      });
    } else if (file.files) {
      // 处理文件夹
      expandedFiles.push({
        ...file,
        isFolder: true,
        last_modified: formatDate(file.last_modified), // 添加文件夹的 last_modified 格式化
        influx_ubique_tagId: file.influx_ubique_tagId,
        start_time: formatDate(file.start_time),
        end_time: formatDate(file.end_time),
      });
    } else {
      // 处理单个文件
      expandedFiles.push({
        ...file,
        last_modified: formatDate(file.last_modified), // 添加单个文件的 last_modified 格式化
        start_time: formatDate(file.start_time),
        end_time: formatDate(file.end_time),
        isFolder: false,
        file_Minio_version_id: file.file_Minio_version_id,
        file_Postgresql_id: file.file_Postgresql_id,
        influx_ubique_tagId: file.influx_ubique_tagId,
      });
    }
  });

  // 按时间降序排序（这里也可以对 last_modified 做格式化）
  return expandedFiles.sort((a, b) => 
    new Date(b.last_modified).getTime() - new Date(a.last_modified).getTime()
  );
};

// 修改获取文件历史记录方法
const getFileHistory = async (project_Name: string, project_Type: string) => {
  console.log('getFileHistory() - project_Name = ', project_Name, 'project_Type = ', project_Type)
  // 检查参数是否有效
  if (!project_Name || !project_Type) {
    ElMessage.warning('项目信息不完整，无法获取文件历史');
    return;
  }

  try {
    // 构建查询参数
    const params = {
      action: 'history',
      project_name: project_Name,
      project_type: project_Type,
    };
    const response = await fetch(
      `${baseBackendUrl}api/data_pipeline/file/minio/history/`,
      {
        method: 'POST',
        credentials: 'include',
        headers: {
          "Authorization": 'JWT ' + Session.get('token'),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      }
    );
    const result = await response.json();
    if (result.code === 2000) {
      console.log('历史记录 -------- ', result.data)
      fileList.value = processFileList(result.data);
    } else {
      ElMessage.error('获取文件历史记录失败');
    }
  } catch (error) {
    console.error('获取文件历史记录失败:', error);
    ElMessage.error('获取文件历史记录失败');
  }
};

const project_type = ref('')
const project_name = ref('')

const isFirstLoad = ref(store.$state.projectInfo.isSelectedChange) // 默认为 false
let isWatchTriggered = ref(false) // 添加标志位追踪watch是否被触发

// 根据所选项目的name和type，从项目列表里筛选Obj, 更新pinia里的项目信息
const singleProPinia = store.$state.projectInfo
watch(singleProPinia, (val, oldVal) => {
  console.log('触发监听watch ---------')
  project_type.value = val.project_type
  project_name.value = val.project_name
  isWatchTriggered.value = true
  
  getFileHistory(project_name.value, project_type.value)
  store.$state.projectInfo.isSelectedChange = true

  console.log('pinia里的 minioInfo = ', toRaw(store.$state.minioInfo))
  console.log('pinia里的 influxdbInfo = ', toRaw(store.$state.influxdbInfo))
})

const getProInfoToPinia = () => {
  project_type.value = store.$state.projectInfo.project_type
  project_name.value = store.$state.projectInfo.project_name
  console.log('project_type.value =', project_type.value, 'project_name.value =', project_name.value)

  console.log('pinia里的 minioInfo = ', toRaw(store.$state.minioInfo))
  console.log('pinia里的 influxdbInfo = ', toRaw(store.$state.influxdbInfo))

  getFileHistory(project_name.value, project_type.value)
}

onMounted(() => {
  console.log('初始化 isFirstLoad.value === ', isFirstLoad.value)
  // 只有在watch没有被触发且isFirstLoad为true时才执行
  if (isFirstLoad.value && !isWatchTriggered.value) {
    getProInfoToPinia()
  }
})

</script>

<style scoped>
.border-2 {
	transition: all 0.3s ease;
}

.border-blue-400 {
	border-width: 3px;
}

.el-table {
	--el-table-border-color: #e5e7eb;
	--el-table-header-bg-color: #f8faff;
	--el-table-row-hover-bg-color: #f0f7ff;
	border-radius: 0.75rem;
	overflow: hidden;
}

.el-table :deep(th.el-table__cell) {
  background-color: var(--el-table-header-bg-color);
  font-weight: 600;
}

.el-table :deep(.el-table__row:hover) {
  cursor: pointer;
}

/* 让表头和表体宽度100% */
.el-table :deep(.el-table__header),
.el-table :deep(.el-table__body) {
  width: 100% !important;
  table-layout: fixed;
}

.el-icon {
  vertical-align: middle;
}

.file-list-container {
  /* 添加容器阴影效果 */
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 0.75rem;
}

/* 自定义滚动条样式 */
.el-table :deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

.el-table :deep(.el-scrollbar__bar.is-vertical) {
  width: 8px;
}

.el-table :deep(.el-scrollbar__thumb) {
  background-color: #909399;
  opacity: 0.3;
}

.el-table :deep(.el-scrollbar__thumb:hover) {
  opacity: 0.5;
}

/* 数据可视化弹窗样式 */
.data-visualization-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.data-visualization-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: 0;
  padding: 20px 24px;
}

.data-visualization-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}
</style>
