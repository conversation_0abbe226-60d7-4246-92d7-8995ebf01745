version: '3.8'

services:
  redis:
    image: artifactory-esc.corp.knorr-bremse.com:8482/docker-public/redis:latest
    container_name: dataPipeline_redis
    environment:
      - REDIS_PORT=6379
      # 设置 Redis 密码（可选，建议生产环境开启）
      # - REDIS_PASSWORD=admin123456
    ports:
      - "6382:6379"  # 映射 Redis 默认端口
    volumes:
      - redis_data:/data  # Redis 持久化数据目录
      - ./redis.conf:/usr/local/etc/redis/redis.conf  # 挂载自定义配置文件（可选）
    command:
      - redis-server
      - /usr/local/etc/redis/redis.conf  # 使用自定义配置文件启动
    networks:
      - datapipeline_network
    restart: always

# 定义持久化卷
volumes:
  redis_data:

# 使用已有网络（datapipeline_network）
networks:
  datapipeline_network:
    external: true