from django.urls import path
from rest_framework.routers import SimpleRouter
from data_pipeline.views.project import DataPipelineProjectModelViewSet
from data_pipeline.views.file import DataPipelineFileModelViewSet, sse_progress_view_file_upload
from .views.influxdb import InfluxDBQueryViewSet
from .views.minio import MinioStorageView, sse_progress_view_minio
from .utils.influx_client import sse_progress_view_influxdb
from .views.redis import RedisView

# 创建路由器
router = SimpleRouter()
router.register("project", DataPipelineProjectModelViewSet)
router.register("file", DataPipelineFileModelViewSet)

# 定义应用的URL模式
app_name = 'data_pipeline'

# 将所有API路由放在data_pipeline前缀下
urlpatterns = [
    path('file/upload/', DataPipelineFileModelViewSet.as_view({'post': 'upload_file'})),
    path('file/sse/upload/', sse_progress_view_file_upload, name='sse_progress_view_file_upload'),
    path('file/influxdb/query/', InfluxDBQueryViewSet.as_view({'post': 'influxdb_query_data'})),
    path('file/minio/history/', MinioStorageView.as_view({'post': 'get_file_upload_history'})),
    path('file/sse/minio/', sse_progress_view_minio, name='sse_progress_view_minio'),
    path('file/sse/influxdb/', sse_progress_view_influxdb, name='sse_progress_view_influxdb'),
    path('file/influxdb/getStatistics/', InfluxDBQueryViewSet.as_view({'post': 'get_data_statistics'})),

    # redis测试
    path('redis/post_data/', RedisView.as_view({'post': 'post_data'})),
    path('redis/update_data/', RedisView.as_view({'put': 'update_data'})),
]
urlpatterns += router.urls