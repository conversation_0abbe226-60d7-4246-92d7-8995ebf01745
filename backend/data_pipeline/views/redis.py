from rest_framework.viewsets import ViewSet
from rest_framework.decorators import action
from rest_framework.response import Response
from django.core.cache import cache
from rest_framework import status

class RedisView(ViewSet):

    @action(detail=False, methods=['post'])
    def post_data(self, request):
        data = request.data
        cache.set('test', data, timeout=3600)
        msg = {
            'message': '数据存入redis成功',
            'redis_data': data
        }
        return Response(msg, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['get'])
    def get_data(self, request):
        data = cache.get('test')
        return Response({
            'message': '数据取出redis成功',
            'data': data
        })

    @action(detail=False, methods=['put'])
    def update_data(self, request):
        data = request.data
        cache.set('test', data, timeout=360)
        msg = {
            'message': '数据更新redis成功',
            'redis_data': data
        }
        return Response(msg)

    @action(detail=False, methods=['delete'])
    def delete_data(self, request):
        deleted = cache.delete('test')
        if deleted:
            return Response({'message': '数据删除redis成功'})
        else:
            return Response({'message': '键不存在'}, status=status.HTTP_404_NOT_FOUND)