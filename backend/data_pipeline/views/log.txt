FileUpload.vue:411 开始处理拖拽文件，项目数量: 3
FileUpload.vue:444 未检测到文件夹，使用传统方式读取文件
FileUpload.vue:448 最终获取到的文件总数: 3
FileUpload.vue:451 准备上传文件: {fileCount: 3, isFolder: false, folderName: '', fileNames: Array(3)}
DataProcess.vue:617 执行 initProgressInfo()
DataProcess.vue:670 !isAllDone ==============  true
DataProcess.vue:673 onMounted() 检查是否需要开始解析
DataProcess.vue:674 isDoneFileUpload.value 检查文件是否已上传到后端 ==============  false
DataProcess.vue:553 等待文件上传完成...
DataProcess.vue:186 SSE建立连接 - file upload fileUploadEventSource
DataProcess.vue:191 file upload data=========== {error: '没有接收到文件'}error: "没有接收到文件"[[Prototype]]: Object
DataProcess.vue:249 处理文件上传进度数据错误： TypeError: Cannot read properties of undefined (reading 'toString')
    at fileUploadEventSource.onmessage (DataProcess.vue:233:1)
fileUploadEventSource.onmessage @ DataProcess.vue:249
DataProcess.vue:263 文件上传SSE连接错误:  Event {isTrusted: true, type: 'error', target: EventSource, currentTarget: EventSource, eventPhase: 2, …}
fileUploadEventSource.onerror @ DataProcess.vue:263
